﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using uBuyFirst.Data;
using uBuyFirst.Network;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Purchasing.Cookies;
using uBuyFirst.Tools;
using static uBuyFirst.Purchasing.BuyingService;

namespace uBuyFirst.Purchasing
{
    /// <summary>
    /// Result of Live Mode prerequisite validation
    /// </summary>
    public class LiveModeValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> MissingRequirements { get; set; } = new();
        public string Summary => IsValid ? "All prerequisites met" : $"Missing: {string.Join(", ", MissingRequirements)}";
    }
    internal static class CreditCardService
    {
        public static bool CreditCardPaymentEnabled { get; set; }
        public static int ActiveSessionCount;

        /// <summary>
        /// Validates all prerequisites required for Live Mode purchasing
        /// </summary>
        /// <returns>Validation result with details about missing requirements</returns>
        public static LiveModeValidationResult ValidateLiveModePrerequisites()
        {
            var result = new LiveModeValidationResult { IsValid = true };

            // Check if restock functionality is enabled
            if (!ConnectionConfig.RestockerEnabled)
            {
                result.IsValid = false;
                result.MissingRequirements.Add("RestockerEnabled must be true in ConnectionConfig");
            }

            // Check if checkout functionality is enabled
            if (!ConnectionConfig.CheckoutEnabled)
            {
                result.IsValid = false;
                result.MissingRequirements.Add("CheckoutEnabled must be true in ConnectionConfig");
            }

            // Check if credit card payment is enabled
            if (!CreditCardPaymentEnabled)
            {
                result.IsValid = false;
                result.MissingRequirements.Add("CreditCardPaymentEnabled must be true");
            }

            // Check if Firefox profile is available
            if (CookieManager.Profile == null)
            {
                result.IsValid = false;
                result.MissingRequirements.Add("Firefox profile must be selected");
            }

            // Check if cookies are available for common eBay domains
            var commonDomains = new[] { ".ebay.com" };
            if (!CookieManager.EnsureCookiesAvailable(commonDomains))
            {
                result.IsValid = false;
                result.MissingRequirements.Add("eBay cookies must be available in Firefox profile");
            }

            return result;
        }

        public static async Task CreditCartCheckoutCompleteSequence(DataList d)
        {
            d.Order.AutoConfirmationAllowed = true;
            await CreateSession(d);

            if (string.IsNullOrEmpty(d.Order.SessionID))
            {
                return;
            }

            // SessionID is confirmed not null here
            d.Order.CheckoutStatus = Order.CheckoutState.SessionCreated; // Status already set in CreateSession, but confirm here

            // UI status updates removed - handled by calling code
            await ConfirmCreditCardPayment((BuyOrder)d.Order, d.Title);
        }

        public static async Task CreateSession(DataList d)
        {
            try
            {
                d.Order.CheckoutStatus = Order.CheckoutState.CreatingSession;
                d.Order._sw.Restart();
                d.Order.PurchaseProgress.Report(" Creating session\n");

                PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Creating session");
                var sessionPageHTML = await SendSessionRequest((BuyOrder)d.Order);
                PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Created session");
                PaymentLogger.LogPayment(d.Order.ItemID, "1", sessionPageHTML);

                var orderSessionID = Helpers.RegexValue(sessionPageHTML, "sessionid=([0-9]+)");
                if (string.IsNullOrEmpty(orderSessionID))
                {
                    orderSessionID = Helpers.RegexValue(sessionPageHTML, "sessionid%3D([0-9]+)");
                    if (string.IsNullOrEmpty(orderSessionID))
                    {
                        orderSessionID = Helpers.RegexValue(sessionPageHTML, "\"si\":\"([0-9]+)");
                        if (string.IsNullOrEmpty(orderSessionID))
                        {
                            switch (sessionPageHTML)
                            {
                                case Config.CaptchaUrl:
                                    d.Order.FailureReasonMessage = Config.CaptchaUrl;
                                    break;
                                case Config.EbaySignInUrl:
                                    d.Order.FailureReasonMessage = Config.EbaySignInUrl;
                                    CookieManager.ClearCookieCache();
                                    //d.Order.CookieContainer = CookieManager.GetCookies(new[] { $".{d.Order.EbaySite.Domain}" });
                                    break;
                                default:
                                    var specificError = ExtractErrorFromSession(sessionPageHTML); // Reuse session error extractor? Or create a new one for confirmation page?
                                    d.Order.FailureReasonMessage = !string.IsNullOrEmpty(specificError) ? specificError : "Payment failed (Confirmation page did not indicate success).";
                                    break;
                            }
                            d.Order.CheckoutStatus = Order.CheckoutState.SessionCreationFailed;
                            return;
                        }
                    }
                }

                //parse session props
                d.Order.SessionHtml = sessionPageHTML;
                d.Order.UserName = GetUserName(sessionPageHTML);
                d.Order.SessionID = orderSessionID;

                if (string.IsNullOrEmpty(d.Order.SessionID))
                {
                    d.Order.CheckoutStatus = Order.CheckoutState.SessionCreationFailed; // Use specific failure state
                    // FailureReasonMessage should already be set if we reached here due to missing ID
                    if (string.IsNullOrEmpty(d.Order.FailureReasonMessage)) // Set a generic one if not already set
                    {
                        d.Order.FailureReasonMessage = "Failed to create session (Unknown reason).";
                    }
                    return;
                }
                else
                {
                    d.Order.CheckoutStatus = Order.CheckoutState.SessionCreated;
                    d.Order.FailureReasonMessage = null; // Clear any previous error message
                    d.Order.PurchaseProgress.Report(" Created session\n");

                    // Save HTML for all checkout sessions to Reports\CheckoutHtml folder
                    await SaveCheckoutHtmlAsync(sessionPageHTML, d.Order.ItemID, d.Order.SessionID, d.Order.IsRestockPurchase);
                }

                d.Order.IsNewCheckout = d.Order.SessionHtml.Contains("COMPLETE_IMMEDIATE_PURCHASE");

                if (d.Order.IsNewCheckout)
                    d.Order.PurchaseProgress.Report("NewCheckout");
                else
                    d.Order.PurchaseProgress.Report("OldCheckout");
            }
            catch (Exception e)
            {
                // Add Live Mode context to session creation errors
                var modeContext = UserSettings.RestockMode == RestockModeEnum.LiveMode ? " (Live Mode)" :
                                 UserSettings.RestockMode == RestockModeEnum.TestMode ? " (Test Mode)" : "";
                d.Order.FailureReasonMessage = $"Checkout Exception{modeContext}: {e.Message}";
                d.Order.CheckoutStatus = Order.CheckoutState.SessionCreationFailed; // Or a generic error state

                // Enhanced logging for Live Mode session creation failures
                if (UserSettings.RestockMode == RestockModeEnum.LiveMode)
                {
                    PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} LIVE MODE: Session creation failed - {e.Message}");
                }
            }
        }

        public static string ExtractErrorFromSession(string html)
        {
            string pattern = @"<script>window\.__APP_INITIAL_STATE__ = (\{.*?\})<\/script>";
            // Find matches
            Match match = Regex.Match(html, pattern, RegexOptions.Singleline);

            if (match.Success)
            {
                // Extracted JSON string
                string jsonContent = match.Groups[1].Value;

                // Step 2: Parse JSON with JSON.NET
                try
                {
                    var jsonObject = JObject.Parse(jsonContent);

                    // Now you can manipulate the jsonObject as needed, for example:
                    Console.WriteLine("Extracted JSON: " + jsonObject.ToString());

                    // Example: Accessing a property (adjust the property path as necessary)
                    var errorText = jsonObject.SelectToken("response.modules.notifications.notifications[0].title.textSpans[0].text")?.ToString();
                    return errorText ?? "";
                }
                catch (JsonException ex)
                {
                    Console.WriteLine("Error parsing JSON: " + ex.Message);
                }
            }

            return "";
        }

        public static async Task ConfirmCreditCardPayment(BuyOrder order, string title)
        {
            try
            {
                if (ConnectionConfig.CheckoutEnabled)
                {
                    if (!UserSettings.SkipBuyConfirmation)
                    {
                        DialogResult dialogResult;

                        // Show Live Mode specific confirmation dialog
                        if (UserSettings.RestockMode == RestockModeEnum.LiveMode)
                        {
                            var purchaseAmount = order.ItemPrice?.Value ?? 0;
                            var currency = order.ItemPrice?.Currency ?? "USD";

                            var message = $"⚠️ LIVE MODE PURCHASE CONFIRMATION ⚠️\n\n" +
                                         $"Item: \"{title}\"\n" +
                                         $"Amount: {purchaseAmount:F2} {currency}\n" +
                                         $"Quantity: {order.Quantity}\n\n" +
                                         $"This will charge your payment method with REAL MONEY!\n\n" +
                                         $"Choose your action:";

                            var result = XtraMessageBox.Show(message + "\n\nYes = Proceed with Live Purchase\nNo = Cancel Purchase",
                                "Live Mode Purchase Confirmation",
                                MessageBoxButtons.YesNo, MessageBoxIcon.Warning,
                                MessageBoxDefaultButton.Button2); // Default to Cancel for safety

                            switch (result)
                            {
                                case DialogResult.Yes:
                                    // Proceed with Live Mode purchase
                                    dialogResult = DialogResult.Yes;
                                    PaymentLogger.LogPaymentToFile($"{order.ItemID} User confirmed Live Mode purchase");
                                    break;
                                case DialogResult.No:
                                    // Cancel the purchase
                                    PaymentLogger.LogPaymentToFile($"{order.ItemID} User cancelled Live Mode purchase");
                                    dialogResult = DialogResult.No;
                                    break;
                                default:
                                    dialogResult = DialogResult.No;
                                    break;
                            }
                        }
                        else
                        {
                            // Standard confirmation for Test Mode or other modes
                            var modeText = UserSettings.RestockMode == RestockModeEnum.TestMode ? " (Test Mode)" : "";
                            dialogResult = XtraMessageBox.Show($"Purchase \"{title}\"{modeText}?", "Confirmation", MessageBoxButtons.YesNo);
                        }

                        if (dialogResult != DialogResult.Yes)
                            return;
                    }

                    PaymentLogger.LogPaymentToFile($"{order.ItemID} Confirming payment");
                    await CreditCardCheckoutFinalConfirmation(order); // This now sets status on the order object
                    PaymentLogger.LogPaymentToFile($"{order.ItemID} Payment confirmed (Status: {order.CheckoutStatus})");
                    // Remove XtraMessageBox. The UI should react to order.CheckoutStatus and order.FailureReasonMessage
                    // Example: if (order.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess) { // Show success in flyout }
                    // Example: else if (order.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentFailed) { // Show failure in flyout with order.FailureReasonMessage }
                }
            }
            catch (Exception e)
            {
                // Add Live Mode context to confirmation errors
                var modeContext = UserSettings.RestockMode == RestockModeEnum.LiveMode ? " (Live Mode)" :
                                 UserSettings.RestockMode == RestockModeEnum.TestMode ? " (Test Mode)" : "";
                order.FailureReasonMessage = $"Confirm Checkout Exception{modeContext}: {e.Message}";
                order.CheckoutStatus = Order.CheckoutState.ConfirmationFailed; // Assuming this state exists

                // Enhanced logging for Live Mode confirmation failures
                if (UserSettings.RestockMode == RestockModeEnum.LiveMode)
                {
                    PaymentLogger.LogPaymentToFile($"{order.ItemID} LIVE MODE: Confirmation failed - {e.Message}");
                }
            }
        }

        private static async Task CreditCardCheckoutFinalConfirmation(BuyOrder order)
        {
            await ConfirmCreditCardCheckout(order); // This now sets status (CheckoutStatus, FailureReasonMessage) on the order

            if (order.CheckoutStatus == Order.CheckoutState.TestPurchase) // Assuming this state exists
            {
                Stats.Pixel.Track(Stats.Pixel.EventType.TestPurchase, order.ItemID);
                order.PurchaseProgress.Report($" ({Math.Floor(order._sw.Elapsed.TotalSeconds)}s) Test Purchase. {order.FailureReasonMessage}\n"); // Report based on status
                return;
            }

            // Update stats based on the status set on the order object
            if (order.CheckoutStatus == Order.CheckoutState.PaymentSuccess) // Assuming this state exists
            {
                order.PurchaseProgress.Report($" ({Math.Floor(order._sw.Elapsed.TotalSeconds)}s) Payment successful\n"); // Report based on status
                Stat.WebCheckoutWonCounter++;
                var paypalWinAmountUSD = CurrencyConverter.ConvertToUSD(order.ItemPrice.Value, order.ItemPrice.Currency);
                Stats.Pixel.Track(Stats.Pixel.EventType.PaypalWin, order.ItemID, paypalWinAmountUSD);
            }
            else // Assume any other state means failure for stats purposes (e.g., PaymentFailed, ConfirmationFailed)
            {
                // FailureReasonMessage should already be set by ConfirmCreditCardCheckout or the catch block in ConfirmCreditCardPayment
                order.PurchaseProgress.Report($" ({Math.Floor(order._sw.Elapsed.TotalSeconds)}s) Payment failed: {order.FailureReasonMessage}\n"); // Report based on status
                Stat.WebCheckoutLostCounter++;
                Stats.Pixel.Track(Stats.Pixel.EventType.PaypalLost, order.ItemID);
            }

            // No return value needed, status is on the order object
        }

        private static async Task ConfirmCreditCardCheckout(BuyOrder order)
        {
            order.PurchaseProgress.Report($" ({Math.Floor(order._sw.Elapsed.TotalSeconds)}s) Confirming payment\n");
            var url = $"https://pay.{order.EbaySite.Domain}/rxo?action=confirm&sessionid={order.SessionID}";

            // Reset status before attempting confirmation
            order.CheckoutStatus = Order.CheckoutState.PaymentInProgress; // Or a more specific state like 'ConfirmingPayment'
            order.FailureReasonMessage = null;

            // Check if we're in test mode - prevent actual payment completion
            if (UserSettings.RestockMode == RestockModeEnum.TestMode)
            {
                order.FailureReasonMessage = "Test mode. Session created. Payment not completed.";
                order.CheckoutStatus = Order.CheckoutState.TestPurchase;
                return; // Exit without making the call
            }

            // If disabled, this shouldn't be called, but handle it gracefully
            if (UserSettings.RestockMode == RestockModeEnum.Disabled)
            {
                order.FailureReasonMessage = "Restock functionality is disabled.";
                order.CheckoutStatus = Order.CheckoutState.PaymentFailed;
                return;
            }

            // Debug mode safety check - overrides all restock modes for developer safety
            if (Debugger.IsAttached
                || ProgramState.SerialNumber.StartsWith("ROMA")
                || ProgramState.SerialNumber.StartsWith("5964-1CE4")//dustin
                || ProgramState.SerialNumber.StartsWith("AADA-AE87")//dustin
                || ProgramState.SerialNumber.StartsWith("8E21-CB91")//dustin
                )
            {
                // Log when debug mode overrides Live Mode
                if (UserSettings.RestockMode == RestockModeEnum.LiveMode)
                {
                    order.PurchaseProgress.Report($" ({Math.Floor(order._sw.Elapsed.TotalSeconds)}s) DEBUG MODE: Live Mode purchase blocked for developer safety\n");
                    order.FailureReasonMessage = "Debug mode active. Live Mode purchase blocked for developer safety.";

                    // Log to payment logger for tracking
                    PaymentLogger.LogPaymentToFile($"{order.ItemID} DEBUG MODE: Live Mode purchase blocked (Serial: {ProgramState.SerialNumber}, Debugger: {Debugger.IsAttached})");
                }
                else
                {
                    order.FailureReasonMessage = "Debugging. Purchase disabled.";
                    PaymentLogger.LogPaymentToFile($"{order.ItemID} DEBUG MODE: Purchase disabled (Serial: {ProgramState.SerialNumber}, Debugger: {Debugger.IsAttached})");
                }

                order.CheckoutStatus = Order.CheckoutState.TestPurchase;
                return; // Exit without making the call
            }

            // Explicit Live Mode validation and handling
            if (UserSettings.RestockMode == RestockModeEnum.LiveMode)
            {
                // Validate all Live Mode prerequisites before proceeding
                var validation = ValidateLiveModePrerequisites();
                if (!validation.IsValid)
                {
                    order.FailureReasonMessage = $"Live Mode prerequisites not met: {validation.Summary}";
                    order.CheckoutStatus = Order.CheckoutState.PaymentFailed;
                    return;
                }

                // Log that we're proceeding with Live Mode purchase
                order.PurchaseProgress.Report($" ({Math.Floor(order._sw.Elapsed.TotalSeconds)}s) Live Mode: Proceeding with actual purchase\n");
            }

            try
            {
                var pageHtml = await NetTools.FetchUrlUsingCookiesAsync(url, order.CookieContainer);
                PaymentLogger.LogPayment(order.ItemID, "2", pageHtml);

                if (pageHtml.Contains("Your order was placed."))
                {
                    order.CheckoutStatus = Order.CheckoutState.PaymentSuccess; // Assuming this state exists
                    order.FailureReasonMessage = null; // Explicitly clear message on success

                    // Log successful Live Mode purchase
                    if (UserSettings.RestockMode == RestockModeEnum.LiveMode)
                    {
                        PaymentLogger.LogPaymentToFile($"{order.ItemID} LIVE MODE: Purchase completed successfully");
                        order.PurchaseProgress.Report($" ({Math.Floor(order._sw.Elapsed.TotalSeconds)}s) Live Mode: Purchase completed successfully\n");
                    }
                }
                else
                {
                    switch (pageHtml)
                    {
                        case Config.CaptchaUrl:
                            order.FailureReasonMessage = Config.CaptchaUrl;
                            break;
                        case Config.EbaySignInUrl:
                            order.FailureReasonMessage = Config.EbaySignInUrl;
                            CookieManager.ClearCookieCache();
                            break;
                        default:
                            var specificError = ExtractErrorFromSession(pageHtml); // Reuse session error extractor? Or create a new one for confirmation page?
                            order.FailureReasonMessage = !string.IsNullOrEmpty(specificError) ? specificError : "Payment failed (Confirmation page did not indicate success).";
                            break;
                    }
                    order.CheckoutStatus = Order.CheckoutState.PaymentFailed; // Assuming this state exists

                    // Log Live Mode payment failure
                    if (UserSettings.RestockMode == RestockModeEnum.LiveMode)
                    {
                        PaymentLogger.LogPaymentToFile($"{order.ItemID} LIVE MODE: Payment failed - {order.FailureReasonMessage}");
                    }
                }
            }
            catch (Exception ex)
            {
                order.FailureReasonMessage = $"Exception during payment confirmation: {ex.Message}";
                order.CheckoutStatus = Order.CheckoutState.PaymentFailed; // Or ConfirmationFailed

                // Enhanced logging for Live Mode exceptions
                if (UserSettings.RestockMode == RestockModeEnum.LiveMode)
                {
                    PaymentLogger.LogPaymentToFile($"{order.ItemID} LIVE MODE: Exception during payment confirmation: {ex.Message}");
                }
                else
                {
                    PaymentLogger.LogPaymentToFile($"{order.ItemID} Exception during payment confirmation: {ex.Message}");
                }
            }
            finally // Ensure SessionID is cleared and status is reset if not successful/failed explicitly
            {
                if (order.CheckoutStatus != Order.CheckoutState.PaymentSuccess &&
                    order.CheckoutStatus != Order.CheckoutState.PaymentFailed) // If status wasn't set due to an early exit or unexpected issue
                {
                    order.CheckoutStatus = Order.CheckoutState.PaymentFailed; // Default to failed if not explicitly success
                    if (string.IsNullOrEmpty(order.FailureReasonMessage))
                    {
                        order.FailureReasonMessage = "Payment confirmation ended inconclusively.";
                    }
                }
                // Clear session details regardless of outcome after attempt
                order.SessionID = "";
                // Consider if CheckoutStatus should be reset to NotStarted here or if PaymentSuccess/PaymentFailed should persist. Let's let it persist.
                // order.CheckoutStatus = BuyingService.Order.CheckoutState.NotStarted;
            }
            // No return value needed
        }

        private static string GetUserName(string pageHtml)
        {
            var userName = Helpers.RegexValue(pageHtml, "\\\"userId\\\":\\\"(.+?)\\\"");
            if (string.IsNullOrEmpty(userName))
            {
                userName = "Not logged in";
                CookieManager.ClearCookieCache();
            }

            return userName;
        }

        private static async Task<string> SendSessionRequest(BuyOrder order)
        {
            var url = $"https://pay.{order.EbaySite.Domain}/rxo?item={order.ItemID}&action=create&transactionid=-1&quantity={order.Quantity}";

            var affiliateParams = AffiliateTool.GetAffiliateParams(order.ItemID, order.EbaySite, "PPCC");
            if (!string.IsNullOrEmpty(affiliateParams))
                url += "&" + affiliateParams;

            var pageHtml = await NetTools.FetchUrlUsingCookiesAsync(url, order.CookieContainer);

            return pageHtml;
        }


        /// <summary>
        /// Saves checkout session HTML for all purchases to Reports\CheckoutHtml folder
        /// </summary>
        private static async Task SaveCheckoutHtmlAsync(string htmlContent, string itemId, string sessionId, bool isRestockPurchase)
        {
            try
            {
                // Create Reports\CheckoutHtml folder structure in settings directory
                var reportsFolder = Path.Combine(Folders.Settings, "Reports", "CheckoutHtml");

                if (!Directory.Exists(reportsFolder))
                {
                    Directory.CreateDirectory(reportsFolder);
                }

                // Patch HTML content (remove historyPushURL like PaymentLogger does)
                var patchedHtml = htmlContent.Replace("historyPushURL", "");

                // Generate filename with timestamp, itemId, sessionId, and purchase type
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
                var sanitizedItemId = SanitizeForFilename(itemId ?? "unknown");
                var sanitizedSessionId = SanitizeForFilename(sessionId ?? "unknown");
                var purchaseType = isRestockPurchase ? "restock" : "manual";
                var fileName = $"{timestamp}_{sanitizedItemId}_{sanitizedSessionId}_{purchaseType}.html";
                var filePath = Path.Combine(reportsFolder, fileName);

                // Write patched HTML content asynchronously
                await Task.Run(() => File.WriteAllText(filePath, patchedHtml));

                // Log success
                var logMessage = isRestockPurchase ? "restock checkout HTML" : "manual checkout HTML";
                PaymentLogger.LogPaymentToFile($"Saved {logMessage}: {fileName}");
            }
            catch (Exception ex)
            {
                // Log error but don't let it break the checkout process
                var purchaseType = isRestockPurchase ? "restock" : "manual";
                PaymentLogger.LogPaymentToFile($"Failed to save {purchaseType} checkout HTML for item {itemId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Sanitizes a string to be safe for use in filenames
        /// </summary>
        private static string SanitizeForFilename(string input)
        {
            if (string.IsNullOrEmpty(input))
                return "unknown";

            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = input;

            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // Also replace some additional problematic characters
            sanitized = sanitized.Replace(' ', '_').Replace(':', '_').Replace('?', '_');

            // Limit length to avoid filesystem issues
            if (sanitized.Length > 50)
                sanitized = sanitized.Substring(0, 50);

            return sanitized;
        }
    }
}
